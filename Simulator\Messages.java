package Simulator;

/**
 * MESSAGES POUR COMMUNICATION ENTRE ROBOTS - VERSION SIMPLIFIÉE
 */

/**
 * CLASSE ENCHERE - OFFRE D'UN ROBOT POUR UNE TÂCHE
 */
class Enchere implements Comparable<Enchere> {
    public String robotId;
    public String taskId;
    public double bidAmount;
    public long timestamp;

    /**
     * MÉTHODE POUR CRÉER UNE NOUVELLE OFFRE
     */
    public Enchere(String robotId, String taskId, double bidAmount) {
        this.robotId = robotId;
        this.taskId = taskId;
        this.bidAmount = bidAmount;
        this.timestamp = System.currentTimeMillis();
    }

    public String getRobotId() { return robotId; }
    public String getTaskId() { return taskId; }
    public double getBidAmount() { return bidAmount; }
    public long getTimestamp() { return timestamp; }

    /**
     * MÉTHODE POUR COMPARER DEUX OFFRES
     * Étape 1 : Comparer les montants (plus élevé = meilleur)
     * Étape 2 : <PERSON> <PERSON>gaux, comparer les timestamps (plus tôt = meilleur)
     */
    @Override
    public int compareTo(Enchere autre) {
        int comparaison = Double.compare(autre.bidAmount, this.bidAmount);
        if (comparaison != 0) return comparaison;
        return Long.compare(this.timestamp, autre.timestamp);
    }
}

/**
 * CLASSE DE BASE POUR LES MESSAGES
 */
abstract class Message {
    public MyTransitRobot sender;
    public String messageType;
    public long timestamp;

    /**
     * MÉTHODE POUR CRÉER UN NOUVEAU MESSAGE
     */
    public Message(MyTransitRobot sender, String messageType) {
        this.sender = sender;
        this.messageType = messageType;
        this.timestamp = System.currentTimeMillis();
    }

    public MyTransitRobot getSender() { return sender; }
    public String getMessageType() { return messageType; }
    public long getTimestamp() { return timestamp; }

    public abstract void process(MyTransitRobot destinataire);
}

/**
 * MESSAGE CONTENANT UNE OFFRE D'ENCHÈRE
 */
class MessageOffre extends Message {
    public Enchere offre;

    /**
     * MÉTHODE POUR CRÉER UN MESSAGE D'OFFRE
     */
    public MessageOffre(MyTransitRobot expediteur, Enchere offre) {
        super(expediteur, "OFFRE");
        this.offre = offre;
    }

    public Enchere getBid() { return offre; }

    /**
     * MÉTHODE POUR TRAITER LE MESSAGE QUAND IL EST REÇU
     */
    @Override
    public void process(MyTransitRobot destinataire) {
        destinataire.getCoordinateurTaches().handleBid(offre);
    }
}

/**
 * MESSAGE POUR ANNONCER UNE NOUVELLE TÂCHE
 */
class NewTaskMessage extends Message {
    public CoordinateurTaches.Task task;

    /**
     * MÉTHODE POUR CRÉER UN MESSAGE DE NOUVELLE TÂCHE
     */
    public NewTaskMessage(MyTransitRobot expediteur, CoordinateurTaches.Task task) {
        super(expediteur, "NEW_TASK");
        this.task = task;
    }

    public CoordinateurTaches.Task getTask() { return task; }

    /**
     * MÉTHODE POUR TRAITER LE MESSAGE QUAND IL EST REÇU
     */
    @Override
    public void process(MyTransitRobot destinataire) {
        destinataire.getCoordinateurTaches().handleNewTask(task);
    }
}

/**
 * MESSAGE POUR NOTIFIER L'ATTRIBUTION D'UNE TÂCHE
 */
class TaskAssignedMessage extends Message {
    public String taskId;
    public String assignedRobotId;

    /**
     * MÉTHODE POUR CRÉER UN MESSAGE D'ATTRIBUTION
     */
    public TaskAssignedMessage(MyTransitRobot expediteur, String taskId, String assignedRobotId) {
        super(expediteur, "TASK_ASSIGNED");
        this.taskId = taskId;
        this.assignedRobotId = assignedRobotId;
    }

    /**
     * MÉTHODE POUR TRAITER LE MESSAGE QUAND IL EST REÇU
     */
    @Override
    public void process(MyTransitRobot destinataire) {
        destinataire.getCoordinateurTaches().handleTaskAssignment(taskId, assignedRobotId);
    }
}