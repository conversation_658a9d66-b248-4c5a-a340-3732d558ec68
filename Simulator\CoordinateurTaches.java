package Simulator;

import fr.emse.fayol.maqit.simulator.environment.*;
import fr.emse.fayol.maqit.simulator.components.*;
import java.util.*;

/**
 * COORDINATEUR DE TÂCHES SIMPLIFIÉ - SYSTÈME D'ENCHÈRES DÉCENTRALISÉ
 *
 * Version étudiante simplifiée pour projet de 4ème année.
 * Gère les enchères entre robots de manière décentralisée.
 */
public class CoordinateurTaches {

    // RÉFÉRENCES PRINCIPALES
    private MyTransitRobot robotProprietaire;
    private ColorGridEnvironment environnement;

    // STRUCTURES DE DONNÉES SIMPLIFIÉES
    private Map<String, List<Enchere>> offresRecues = new HashMap<>();
    private Map<String, String> attributionsTaches = new HashMap<>();
    private Map<String, Double> scoresEfficaciteRobots = new HashMap<>();

    // CONSTANTES SIMPLIFIÉES
    private static final long DUREE_ENCHERE_MS = 1000; // 1 seconde

    /**
     * CLASSE TASK SIMPLIFIÉE
     */
    public static class Task {
        private String id;
        private int startX, startY, goalX, goalY;
        private String assignedRobot;
        private long creationTime;
        private ColorPackage packageRef;

        /**
         * MÉTHODE POUR CRÉER UNE NOUVELLE TÂCHE
         */
        public Task(String id, int startX, int startY, int goalX, int goalY, ColorPackage packageRef) {
            this.id = id;
            this.startX = startX;
            this.startY = startY;
            this.goalX = goalX;
            this.goalY = goalY;
            this.packageRef = packageRef;
            this.creationTime = System.currentTimeMillis();
            this.assignedRobot = null;
        }

        // GETTERS ESSENTIELS
        public String getId() { return id; }
        public int getStartX() { return startX; }
        public int getStartY() { return startY; }
        public int getGoalX() { return goalX; }
        public int getGoalY() { return goalY; }
        public String getAssignedRobot() { return assignedRobot; }
        public long getCreationTime() { return creationTime; }
        public ColorPackage getPackageRef() { return packageRef; }

        public void setAssignedRobot(String robotId) { this.assignedRobot = robotId; }
    }

    /**
     * MÉTHODE POUR CRÉER UN COORDINATEUR SIMPLIFIÉ
     */
    public CoordinateurTaches(MyTransitRobot robotProprietaire, ColorGridEnvironment env) {
        this.robotProprietaire = robotProprietaire;
        this.environnement = env;

        // Initialiser l'efficacité du robot
        scoresEfficaciteRobots.put(robotProprietaire.getName(), 1.0);

        LogManager.getInstance().logCoordination(robotProprietaire.getName(), "Coordinateur initialisé");
    }

    /**
     * MÉTHODE POUR TRAITER UNE NOUVELLE TÂCHE (SIMPLIFIÉE)
     */
    public void handleNewTask(Task tache) {
        LogManager.getInstance().logCoordination(robotProprietaire.getName(), "Nouvelle tâche: " + tache.getId());

        // Générer une offre simple basée sur la distance
        double distance = calculerDistance(robotProprietaire.getX(), robotProprietaire.getY(),
                                          tache.getStartX(), tache.getStartY());
        double offre = 100.0 - distance; // Plus proche = meilleure offre

        if (offre > 0) {
            Enchere enchere = new Enchere(robotProprietaire.getName(), tache.getId(), offre);
            MessageOffre message = new MessageOffre(robotProprietaire, enchere);
            robotProprietaire.broadcastMessage(message);

            LogManager.getInstance().logCoordination(robotProprietaire.getName(),
                "Offre de " + String.format("%.2f", offre) + " pour " + tache.getId());
        }
    }

    /**
     * MÉTHODE POUR TRAITER UNE OFFRE REÇUE
     */
    public void handleBid(Enchere offre) {
        String taskId = offre.getTaskId();
        if (!offresRecues.containsKey(taskId)) {
            offresRecues.put(taskId, new ArrayList<>());
        }
        offresRecues.get(taskId).add(offre);

        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Offre reçue de " + offre.getRobotId() + " pour " + taskId);

        // Traiter les offres après un délai simplifié
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                traiterOffres(taskId);
            }
        }, DUREE_ENCHERE_MS);
    }

    /**
     * MÉTHODE POUR TRAITER LES OFFRES D'UNE TÂCHE
     */
    private void traiterOffres(String taskId) {
        List<Enchere> offres = offresRecues.get(taskId);
        if (offres == null || offres.isEmpty()) return;

        // Trier les offres (meilleure en premier)
        Collections.sort(offres);

        // Attribuer à la meilleure offre
        Enchere meilleureOffre = offres.get(0);
        attributionsTaches.put(taskId, meilleureOffre.getRobotId());

        // Notifier l'attribution
        TaskAssignedMessage message = new TaskAssignedMessage(robotProprietaire, taskId, meilleureOffre.getRobotId());
        robotProprietaire.broadcastMessage(message);

        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Tâche " + taskId + " attribuée à " + meilleureOffre.getRobotId());
    }

    /**
     * MÉTHODE POUR TRAITER L'ATTRIBUTION D'UNE TÂCHE
     */
    public void handleTaskAssignment(String taskId, String assignedRobotId) {
        attributionsTaches.put(taskId, assignedRobotId);
        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Attribution reçue: " + taskId + " -> " + assignedRobotId);
    }

    /**
     * MÉTHODE POUR METTRE À JOUR L'EFFICACITÉ D'UN ROBOT
     */
    public void updateRobotEfficiency(MyTransitRobot robot, long deliveryTime, double batteryUsed) {
        // Calcul d'efficacité simplifié
        double efficiency = 1000.0 / (deliveryTime / 1000.0 + batteryUsed);
        scoresEfficaciteRobots.put(robot.getName(), efficiency);

        LogManager.getInstance().logCoordination(robotProprietaire.getName(),
            "Efficacité mise à jour pour " + robot.getName() + ": " + String.format("%.2f", efficiency));
    }

    /**
     * MÉTHODE POUR CALCULER LA DISTANCE MANHATTAN SIMPLIFIÉE
     */
    private double calculerDistance(int x1, int y1, int x2, int y2) {
        return Math.abs(x1 - x2) + Math.abs(y1 - y2);
    }

    /**
     * MÉTHODE POUR OBTENIR LE SCORE D'EFFICACITÉ D'UN ROBOT
     */
    public double getRobotEfficiency(String robotId) {
        return scoresEfficaciteRobots.getOrDefault(robotId, 1.0);
    }

    /**
     * MÉTHODE POUR VÉRIFIER SI UNE TÂCHE EST ATTRIBUÉE
     */
    public boolean isTaskAssigned(String taskId) {
        return attributionsTaches.containsKey(taskId);
    }

    /**
     * MÉTHODE POUR OBTENIR LE ROBOT ASSIGNÉ À UNE TÂCHE
     */
    public String getAssignedRobot(String taskId) {
        return attributionsTaches.get(taskId);
    }
}
