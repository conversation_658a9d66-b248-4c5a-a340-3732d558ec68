package Simulator;

import fr.emse.fayol.maqit.simulator.environment.*;
import fr.emse.fayol.maqit.simulator.components.*;
import java.awt.Color;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * ROBOT DE TRANSIT AVEC SYSTÈME D'ENCHÈRES DÉCENTRALISÉ - VERSION SIMPLIFIÉE
 */
public class MyTransitRobot extends MyRobot {

    // ÉTATS DU ROBOT
    public enum TransitState {
        FREE, GOING_TO_START, GOING_TO_TRANSIT, GOING_TO_GOAL, RETURNING_TO_CENTER
    }

    // VARIABLES D'ÉTAT
    private TransitState transitState;
    private int transitX, transitY;
    private double batteryLevel = 100.0;
    private boolean isCharging = false;
    private CoordinateurTaches taskCoordinator;
    private long momentDepart;
    private int destinationX, destinationY;

    // CONSTANTES
    private static final double MAX_BATTERY = 100.0;
    private static final double CRITICAL_BATTERY_THRESHOLD = 30.0;
    private static final double MOVE_BATTERY_COST = 1.0;
    private static final double PICKUP_BATTERY_COST = 1.0;
    private static final double DEPOSIT_BATTERY_COST = 1.0;
    private static final double CHARGING_RATE = 10.0;
    private static final double TARGET_BATTERY_LEVEL = 80.0;
    private static final int CENTRAL_AREA_X = 10;
    private static final int CENTRAL_AREA_Y = 12;

    // ZONES ET STATIONS
    int[][] transitZones = {{12, 10}, {12, 9}, {9, 10}, {9, 9}};
    int[][] chargingStations = {{11, 10}, {13, 9}, {8, 10}, {10, 9}, {2, 2}, {17, 2}, {2, 17}, {17, 17}};

    // DESTINATIONS - COMPATIBLES AVEC LES COLIS
    private static final Map<Integer, int[]> DESTINATIONS_TRANSIT = new HashMap<Integer, int[]>() {{
        put(1, new int[]{5, 0});   // Zone Z1 - Destination 1
        put(2, new int[]{15, 0});  // Zone Z2 - Destination 2
    }};

    /**
     * MÉTHODE POUR CRÉER UN NOUVEAU ROBOT DE TRANSIT
     */
    public MyTransitRobot(String name, int field, int debug, int[] pos, Color color, int rows, int columns, ColorGridEnvironment env, long seed) {
        super(name, field, debug, pos, color, rows, columns, env, seed);
        this.transitState = TransitState.FREE;
        this.batteryLevel = MAX_BATTERY;
        this.taskCoordinator = new CoordinateurTaches(this, env);
        LogManager.getInstance().logAction(name, "Robot de transit initialisé");
    }

    /**
     * MÉTHODE POUR RÉCUPÉRER LE COORDINATEUR
     */
    public CoordinateurTaches getTaskCoordinator() { return taskCoordinator; }
    public CoordinateurTaches getCoordinateurTaches() { return taskCoordinator; }

    /**
     * MÉTHODE POUR DIFFUSER UN MESSAGE
     */
    public void broadcastMessage(Message message) {
        List<Robot> robots = environnement.getRobot();
        int count = 0;
        for (Robot robot : robots) {
            if (robot != this && robot instanceof MyTransitRobot) {
                ((MyTransitRobot)robot).handleMessage(message);
                count++;
            }
        }
        LogManager.getInstance().logCoordination(getName(), "Message diffusé à " + count + " robots");
    }

    /**
     * MÉTHODE POUR ENVOYER UN MESSAGE DIRECT
     */
    public void sendDirectMessage(Message message, String targetRobotId) {
        List<Robot> robots = environnement.getRobot();
        for (Robot robot : robots) {
            if (robot instanceof MyTransitRobot && robot.getName().equals(targetRobotId)) {
                ((MyTransitRobot)robot).handleMessage(message);
                LogManager.getInstance().logCoordination(getName(), "Message direct envoyé à " + targetRobotId);
                break;
            }
        }
    }

    public double getBatteryLevel() { return batteryLevel; }
    public boolean hasDelivered() { return etatActuel == EtatRobot.LIVRE; }

    private ColorTransitZone findTransitZoneNotFull() {
        for (int[] pos : transitZones) {
            Cell cell = environnement.getGrid()[pos[0]][pos[1]];
            if (cell instanceof ColorCell && ((ColorCell)cell).getContent() instanceof ColorTransitZone) {
                ColorTransitZone tz = (ColorTransitZone) ((ColorCell)cell).getContent();
                if (!tz.isFull()) {
                    transitX = pos[0];
                    transitY = pos[1];
                    return tz;
                }
            }
        }
        return null;
    }

    private ColorTransitZone findTransitZoneWithPackage() {
        for (int[] pos : transitZones) {
            Cell cell = environnement.getGrid()[pos[0]][pos[1]];
            if (cell instanceof ColorCell && ((ColorCell)cell).getContent() instanceof ColorTransitZone) {
                ColorTransitZone tz = (ColorTransitZone) ((ColorCell)cell).getContent();
                if (tz.getPackages().size() > 0) {
                    transitX = pos[0];
                    transitY = pos[1];
                    return tz;
                }
            }
        }
        return null;
    }

    @Override
    public void step() {
        if (batteryLevel > 0) {
            batteryLevel -= MOVE_BATTERY_COST;
            updateRobotColor();
        }

        if (isCharging) {
            if (batteryLevel < TARGET_BATTERY_LEVEL) {
                chargeBattery();
                return;
            } else {
                isCharging = false;
                transitState = TransitState.FREE;
            }
        }

        if (batteryLevel <= CRITICAL_BATTERY_THRESHOLD && !isCharging) {
            goToNearestChargingStation();
            return;
        }

        switch (transitState) {
            case FREE: handleFreeState(); break;
            case GOING_TO_START: handleGoingToStartState(); break;
            case GOING_TO_TRANSIT: handleGoingToTransitState(); break;
            case GOING_TO_GOAL: handleGoingToGoalState(); break;
            case RETURNING_TO_CENTER: handleReturningToCenterState(); break;
        }
    }

    /**
     * MÉTHODE POUR GÉRER L'ÉTAT LIBRE
     */
    private void handleFreeState() {
        // Étape 1 : Chercher un colis dans une zone de transit (priorité haute)
        ColorTransitZone tzWithPackage = findTransitZoneWithPackage();
        if (tzWithPackage != null) {
            transitState = TransitState.GOING_TO_TRANSIT;
            LogManager.getInstance().logAction(getName(), "Va récupérer un colis en zone de transit");
            return;
        }

        // Étape 2 : Chercher un nouveau colis dans les zones de départ
        ColorStartZone startZone = findStartZoneWithPackage();
        if (startZone != null) {
            transitState = TransitState.GOING_TO_START;
            LogManager.getInstance().logAction(getName(), "Va chercher un nouveau colis");

            // Étape 3 : Créer une tâche pour le coordinateur (système d'enchères)
            if (taskCoordinator != null && !startZone.getPackages().isEmpty()) {
                ColorPackage pkg = startZone.getPackages().get(0);
                int[] goalPos = DESTINATIONS_TRANSIT.get(pkg.getDestinationGoalId());
                if (goalPos != null) {
                    CoordinateurTaches.Task task = new CoordinateurTaches.Task(
                        "task_" + pkg.getId(),
                        startZone.getX(), startZone.getY(),
                        goalPos[0], goalPos[1],
                        pkg
                    );
                    taskCoordinator.handleNewTask(task);
                }
            }
            return;
        }

        // Étape 4 : Retourner au centre si rien à faire
        if (getX() != CENTRAL_AREA_X || getY() != CENTRAL_AREA_Y) {
            transitState = TransitState.RETURNING_TO_CENTER;
            LogManager.getInstance().logAction(getName(), "Retourne au centre");
        }
    }

    /**
     * MÉTHODE POUR GÉRER L'ÉTAT ALLER VERS DÉPART
     */
    private void handleGoingToStartState() {
        ColorStartZone startZone = findStartZoneWithPackage();
        if (startZone != null) {
            if (moveTowards(startZone.getX(), startZone.getY())) {
                // Arrivé à la zone de départ, prendre le colis
                ColorPackage pkg = startZone.getPackages().get(0);
                if (pkg != null) {
                    startZone.removePackage(pkg);
                    colisTransporte = pkg;
                    consumeBatteryForPickup();
                    momentDepart = System.currentTimeMillis();

                    int[] goalPos = DESTINATIONS_TRANSIT.get(pkg.getDestinationGoalId());
                    if (goalPos != null) {
                        destinationX = goalPos[0];
                        destinationY = goalPos[1];

                        // Décider d'utiliser une zone de transit ou livraison directe
                        ColorTransitZone tz = findTransitZoneNotFull();
                        if (tz != null && shouldUseTransit(destinationX, destinationY)) {
                            transitState = TransitState.GOING_TO_TRANSIT;
                        } else {
                            transitState = TransitState.GOING_TO_GOAL;
                        }
                    }
                }
            }
        } else {
            transitState = TransitState.FREE;
        }
    }

    /**
     * MÉTHODE POUR GÉRER L'ÉTAT ALLER VERS TRANSIT
     */
    private void handleGoingToTransitState() {
        if (colisTransporte != null) {
            // Déposer le colis dans la zone de transit
            ColorTransitZone tz = findTransitZoneNotFull();
            if (tz != null) {
                if (moveTowards(transitX, transitY)) {
                    tz.addPackage(colisTransporte);
                    consumeBatteryForDeposit();
                    colisTransporte = null;
                    transitState = TransitState.FREE;
                    LogManager.getInstance().logAction(getName(), "Colis déposé en zone de transit");
                }
            } else {
                // Pas de zone de transit disponible, livraison directe
                transitState = TransitState.GOING_TO_GOAL;
            }
        } else {
            // Récupérer un colis de la zone de transit
            ColorTransitZone tz = findTransitZoneWithPackage();
            if (tz != null) {
                if (moveTowards(transitX, transitY)) {
                    if (tz.getPackages().size() > 0) {
                        colisTransporte = tz.getPackages().get(0);
                        tz.removePackage(colisTransporte);
                        consumeBatteryForPickup();
                        momentDepart = System.currentTimeMillis();

                        int[] goalPos = DESTINATIONS_TRANSIT.get(colisTransporte.getDestinationGoalId());
                        if (goalPos != null) {
                            destinationX = goalPos[0];
                            destinationY = goalPos[1];
                            transitState = TransitState.GOING_TO_GOAL;
                            LogManager.getInstance().logAction(getName(), "Colis récupéré de la zone de transit");
                        }
                    }
                }
            } else {
                // Plus de colis en transit, retourner à l'état libre
                transitState = TransitState.FREE;
            }
        }
    }

    /**
     * MÉTHODE POUR GÉRER L'ÉTAT ALLER VERS DESTINATION
     */
    private void handleGoingToGoalState() {
        if (colisTransporte != null) {
            if (moveTowards(destinationX, destinationY)) {
                // Livrer le colis
                long deliveryTime = System.currentTimeMillis() - momentDepart;
                double batteryUsed = MAX_BATTERY - batteryLevel;

                taskCoordinator.updateRobotEfficiency(this, deliveryTime, batteryUsed);

                colisTransporte = null;
                etatActuel = EtatRobot.LIVRE;
                transitState = TransitState.RETURNING_TO_CENTER;

                LogManager.getInstance().logAction(getName(), "Colis livré avec succès");
            }
        } else {
            transitState = TransitState.FREE;
        }
    }

    /**
     * MÉTHODE POUR GÉRER LE RETOUR AU CENTRE
     */
    private void handleReturningToCenterState() {
        if (moveTowards(CENTRAL_AREA_X, CENTRAL_AREA_Y)) {
            transitState = TransitState.FREE;
            etatActuel = EtatRobot.LIBRE;
        }
    }

    /**
     * MÉTHODE POUR DÉCIDER D'UTILISER UNE ZONE DE TRANSIT (SIMPLIFIÉE)
     */
    private boolean shouldUseTransit(int destX, int destY) {
        // Étape 1 : Calculer la distance directe vers la destination
        double directDistance = distanceTo(getX(), getY(), destX, destY);

        // Étape 2 : Trouver la zone de transit la plus proche
        ColorTransitZone nearestTransit = findNearestTransitZone();
        if (nearestTransit == null) {
            return false; // Pas de zone de transit disponible
        }

        // Étape 3 : Calculer la distance via la zone de transit
        double transitDistance = distanceTo(getX(), getY(), transitX, transitY) +
                               distanceTo(transitX, transitY, destX, destY);

        // Étape 4 : Utiliser le transit si c'est pas trop plus long (marge de 30%)
        return transitDistance <= directDistance * 1.3;
    }

    /**
     * MÉTHODE POUR TROUVER LA ZONE DE TRANSIT LA PLUS PROCHE
     */
    private ColorTransitZone findNearestTransitZone() {
        ColorTransitZone nearest = null;
        double minDistance = Double.MAX_VALUE;

        for (int[] pos : transitZones) {
            Cell cell = environnement.getGrid()[pos[0]][pos[1]];
            if (cell instanceof ColorCell && ((ColorCell)cell).getContent() instanceof ColorTransitZone) {
                ColorTransitZone tz = (ColorTransitZone) ((ColorCell)cell).getContent();
                if (!tz.isFull()) {
                    double distance = distanceTo(getX(), getY(), pos[0], pos[1]);
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearest = tz;
                        transitX = pos[0];
                        transitY = pos[1];
                    }
                }
            }
        }
        return nearest;
    }

    /**
     * MÉTHODE POUR ALLER À LA STATION DE RECHARGE LA PLUS PROCHE
     */
    private void goToNearestChargingStation() {
        int[] nearest = findNearestChargingStation();
        if (nearest != null) {
            if (moveTowards(nearest[0], nearest[1])) {
                isCharging = true;
            }
        }
    }

    /**
     * MÉTHODE POUR TROUVER LA STATION DE RECHARGE LA PLUS PROCHE
     */
    private int[] findNearestChargingStation() {
        int[] nearest = null;
        double minDistance = Double.MAX_VALUE;
        for (int[] station : chargingStations) {
            double distance = distanceTo(getX(), getY(), station[0], station[1]);
            if (distance < minDistance) {
                minDistance = distance;
                nearest = station;
            }
        }
        return nearest;
    }

    /**
     * MÉTHODE POUR CHARGER LA BATTERIE
     */
    private void chargeBattery() {
        batteryLevel += CHARGING_RATE;
        if (batteryLevel >= MAX_BATTERY) {
            batteryLevel = MAX_BATTERY;
        }
        updateRobotColor();
    }

    /**
     * MÉTHODE POUR CONSOMMER LA BATTERIE LORS DE LA RÉCUPÉRATION
     */
    public void consumeBatteryForPickup() {
        batteryLevel -= PICKUP_BATTERY_COST;
        updateRobotColor();
    }

    /**
     * MÉTHODE POUR CONSOMMER LA BATTERIE LORS DU DÉPÔT
     */
    public void consumeBatteryForDeposit() {
        batteryLevel -= DEPOSIT_BATTERY_COST;
        updateRobotColor();
    }

    /**
     * MÉTHODE POUR SE DÉPLACER VERS UNE POSITION
     */
    private boolean moveTowards(int targetX, int targetY) {
        if (getX() == targetX && getY() == targetY) {
            return true; // Déjà arrivé
        }
        moveOneStepTo(targetX, targetY);
        return false;
    }

    /**
     * MÉTHODE POUR METTRE À JOUR LA COULEUR DU ROBOT
     */
    private void updateRobotColor() {
        // Couleur basée sur le niveau de batterie (simplifiée)
        if (batteryLevel > 60) {
            setColor(new int[]{0, 255, 0}); // Vert
        } else if (batteryLevel > 30) {
            setColor(new int[]{255, 165, 0}); // Orange
        } else {
            setColor(new int[]{255, 0, 0}); // Rouge
        }
    }

    /**
     * MÉTHODE POUR CONSOMMER LA BATTERIE LORS DU MOUVEMENT
     */
    public void consumeBatteryForMovement() {
        batteryLevel -= MOVE_BATTERY_COST;
        updateRobotColor();
    }

    /**
     * MÉTHODE POUR TRAITER LES MESSAGES REÇUS
     */
    public void handleMessage(Message message) {
        if (message != null) {
            message.process(this);
        }
    }
}
